# Example AWS Pipeline Configuration
# This demonstrates how to use the AWS pipeline infrastructure

trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - terraform/aws/*

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1]) }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: EDV-aws-iactest
  values:
  - EDV-aws-iactest

- name: terraformProjectLocation
  type: string
  default: terraform/aws/example
  values:
  - terraform/aws/example
  - terraform/aws/vpc
  - terraform/aws/ec2

- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy
    - test

- name: skip_checkov
  type: boolean
  default: false

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: ' '

- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'AWS Pipeline Variables'
  - template: env/${{ parameters.environment }}.yaml@pipelines

resources:
  repositories:
    - repository: pipelines
      type: git
      name: pipelines
      ref: refs/tags/v1

extends:
  template: iac-pipelines/iac-execute.yaml@pipelines
  parameters:
    action: ${{ parameters.action }}
    environment: ${{ parameters.environment }}
    terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    skipCheckovScan: ${{ parameters.skip_checkov }}
    appCode: ${{ variables.appCode }}
    
    # AWS Configuration
    awsServiceConnectionName: ${{ variables.awsServiceConnectionName }}
    awsRegion: ${{ variables.awsRegion }}
    awsS3BucketName: ${{ variables.awsS3BucketName }}
    awsS3BucketRegion: ${{ variables.awsS3BucketRegion }}
    awsDynamoDBTableName: ${{ variables.awsDynamoDBTableName }}
    
    terraformVersion: '1.9.8'
