trigger: none

variables:
  System.Debug: true
  CheckoutDirectory: '$(Agent.TempDirectory)/repo'
  CheckoutRepositoryOrg: "ADOS-OTPHU-01"
  CheckoutRepositoryName: '${{ parameters.gcp_lz_semver.resource.repository.name}}'
  CheckoutRepositoryProject: '${{ parameters.gcp_lz_semver.resource.repository.project.name}}'
  CheckoutRepository: 'git://$(CheckoutRepositoryProject)/$(CheckoutRepositoryName)'

pool: PRD-AksPool-centralagent-Deploy

resources:
  repositories:
    - repository: pipelines
      type: git
      name: pipelines
      ref: main #refs/tags/v0.1.0

  webhooks:
    - webhook: gcp_lz_semver
      connection: gcp_lz_semver

stages:
  - stage: semver
    displayName: 'Semantic Versioning'
    jobs:
    - job: gitversion
      variables:
        CommitMessage: '${{ parameters.gcp_lz_semver.detailedMessage.text }}'
        GitVersionConfig: '$(Build.SourcesDirectory)/.config/GitVersion.yml'
        CustomGitVersionConfig: '$(CheckoutDirectory)/.config/GitVersion.yml'
        FallbackGitVersionConfig: '$(Build.SourcesDirectory)/pipelines/GitVersion.yml'
        ChangelogFile: $(Agent.TempDirectory)/changelog.md
      workspace:
        clean: all

      steps:
        - checkout: self
        - bash: |
            cat <<EOF
              ${{ convertToJson(parameters.gcp_lz_semver) }}
            EOF
          displayName: Dump webhook trigger payload

        - bash: |
            source $(Build.SourcesDirectory)/pipelines/scripts/pr_tools.sh
            baseUrl="${{parameters.gcp_lz_semver.resourceContainers.project.baseUrl}}"
            project="${{parameters.gcp_lz_semver.resource.repository.project.name}}"
            pr_id=$(parse_pr_id "$(CommitMessage)")
            get_pr_description "$baseUrl" "$project" "$pr_id" "$(System.AccessToken)" > $(ChangelogFile)
            echo "PR Id: ${pr_id}"
            echo "PR Description:"
            cat $(ChangelogFile)
            echo "##vso[task.setvariable variable=pr_id;]${pr_id}"
          failOnStderr: "false"
          displayName: Fetch PR info

        - bash: |
            echo "CheckoutDirectory: $(CheckoutDirectory)"
            echo "CheckoutRepository: $(CheckoutRepository)"
            echo "Agent.TempDirectory:  $(Agent.TempDirectory)"
            echo "CustomGitVersionConfig: $(CustomGitVersionConfig)"
            echo "GitVersionConfig: $(GitVersionConfig)"

            ls -la $(Agent.TempDirectory)
          displayName: logging

### CHECKOUT ###
        - task: AzureKeyVault@2
          displayName: Load secrets from Key Vault
          inputs:
            azureSubscription: "PRD-gcp-lz-sub-prd-01-FED"
            KeyVaultName: "kvau-weu-prd-RITM2221137"
            SecretsFilter: "PAT-iacbot-contribute"
        - task: Bash@3
          inputs:
            targetType: 'inline'
            script: |
              mkdir -p $(CheckoutDirectory)
              git clone --progress --verbose https://$(PAT-iacbot-contribute)@dev.azure.com/$(CheckoutRepositoryOrg)/$(CheckoutRepositoryProject)/_git/$(CheckoutRepositoryName)/ $(CheckoutDirectory)
          displayName: Checkout repository

### Generate next version ###
        - task: Bash@3
          displayName: Configure GitVersion
          inputs:
            targetType: 'inline'
            script: |
              if [ -e $(CustomGitVersionConfig) ]; then
                cp $(CustomGitVersionConfig) $(GitVersionConfig)
                echo "Using ${{ parameters.gcp_lz_semver.resource.repository.name }} specific GitVersion config"
              else
                cp $(FallbackGitVersionConfig) $(GitVersionConfig)
                echo "Using common GitVersion config"
              fi
              cat $(GitVersionConfig)

        - task: Bash@3
          displayName: Get version
          inputs:
            targetType: 'inline'
            script: |
              gitversion $(CheckoutDirectory) -output buildserver -config $(GitVersionConfig)
            workingDirectory: $(CheckoutDirectory)

        - task: Bash@3
          displayName: Tag latest commit
          inputs:
            targetType: 'inline'
            script: |
              #echo "##vso[build.updatebuildnumber]${{ parameters.gcp_lz_semver.resource.repository.name }}_$(GitVersion.MajorMinorPatch)"
              set -e
              git config user.name "${{ parameters.gcp_lz_semver.resource.pushedBy.displayName }}"
              git config user.email "${{ parameters.gcp_lz_semver.resource.pushedBy.uniqueName }}"
              git tag --annotate --message "PR #$(pr_id)" --message "$(< $(ChangelogFile))" v$(GitVersion.MajorMinorPatch)
              git tag --force "v$(GitVersion.Major).$(Gitversion.Minor)"
              git push --force --verbose --tags
            workingDirectory: $(CheckoutDirectory)