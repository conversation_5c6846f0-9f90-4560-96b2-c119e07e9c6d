mode: Mainline
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
update-build-number: false

# merge-message-formats: {}
# branches:
#   main:
#     regex: ^master$|^main$
#     tag: ''
#     increment: Patch
#     prevent-increment-of-merged-branch-version: true
#     track-merge-target: false
#     source-branches: []
#     tracks-release-branches: false
#     is-release-branch: false
#     is-mainline: true
#     pre-release-weight: 55000