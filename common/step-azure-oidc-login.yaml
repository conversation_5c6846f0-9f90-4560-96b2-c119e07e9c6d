parameters:
  - name: armServiceConnection
  - name: subscriptionID
    default: ''


steps:
- task: AzureCLI@2
  displayName: Login using an ARM Service Connection (OIDC)
  inputs:
    azureSubscription: ${{ parameters.armServiceConnection }}
    scriptType: "bash"
    scriptLocation: inlineScript
    inlineScript: |
      set -eu  # fail on error
      subscriptionId=$(az account show --query id -o tsv)
      if [[ -n "${{ parameters.subscriptionID }}" ]]; then
        subscriptionId=${{ parameters.subscriptionID }}
      fi
      echo "##vso[task.setvariable variable=ARM_CLIENT_ID]$servicePrincipalId"
      echo "##vso[task.setvariable variable=ARM_OIDC_TOKEN;issecret=true]$idToken"
      echo "##vso[task.setvariable variable=ARM_SUBSCRIPTION_ID]$subscriptionId"
      echo "##vso[task.setvariable variable=ARM_TENANT_ID]$tenantId"
    addSpnToEnvironment: true