import json
import os
import collections.abc
from glob import glob
import hcl2
from datetime import datetime
import html
import yaml

# Define color codes
COLORS = {
    'local': '\033[93m',  # Bright Yellow
    'config': '\033[92m',  # <PERSON> Green
    'override': '\033[91m',  # Bright Red
    'reset': '\033[0m'  # Reset
}

# Get workflow inputs with fallback logic
config_directory = os.getenv('CONFIG_DIRECTORY', '')
config_directory_dev = os.getenv('CONFIG_DIRECTORY_DEV', '')
config_directory_prod = os.getenv('CONFIG_DIRECTORY_PROD', '')
config_directory_override = os.getenv('CONFIG_DIRECTORY_OVERRIDE', '')
workspace = os.getenv('WORKSPACE', '$(Pipeline.Workspace)')

print(f"Base config directory: {config_directory}")
print(f"Dev config directory: {config_directory_dev}")
print(f"Prod config directory: {config_directory_prod}")
print(f"Override config directory: {config_directory_override}")
print(f"Workspace: {workspace}")

# Determine actual config directories for each source type
def determine_config_directory(specific_dir, fallback_dir):
    """Determine which config directory to use with fallback logic"""
    return specific_dir if specific_dir else fallback_dir

# Set config directories for each source type
local_config_dir = determine_config_directory(config_directory_dev, config_directory)
shared_config_dir = determine_config_directory(config_directory_prod, config_directory)
override_config_dir = determine_config_directory(config_directory_override, config_directory)

print(f"Using local config directory: {local_config_dir}")
print(f"Using shared config directory: {shared_config_dir}")
print(f"Using override config directory: {override_config_dir}")

# Helper function to load and validate JSON configuration
def load_json(pattern, source):
    configs = []
    for file_path in glob(pattern, recursive=True):
        print(f"Found JSON file: {file_path}")
        try:
            with open(file_path, 'r') as f:
                config = json.load(f)
                configs.append(config)
                print(f"Loaded JSON configuration from {file_path}: {config}")
        except json.JSONDecodeError as e:
            print(f"JSON syntax error in {file_path}: {e}")
            exit(1)
    return configs

# Helper function to load tfvars configuration
def load_tfvars(pattern, source):
    configs = []
    for file_path in glob(pattern, recursive=True):
        print(f"Found tfvars file: {file_path}")
        try:
            with open(file_path, 'r') as f:
                config = hcl2.load(f)
                configs.append(config)
                print(f"Loaded tfvars configuration from {file_path}: {config}")
        except Exception as e:
            print(f"Error loading tfvars file {file_path}: {e}")
            exit(1)
    return configs

# Helper function to load YAML configuration
def load_yaml(pattern, source):
    configs = []
    for file_path in glob(pattern, recursive=True):
        print(f"Found YAML file: {file_path}")
        try:
            with open(file_path, 'r') as f:
                config = yaml.safe_load(f)
                if config is not None:  # Handle empty YAML files
                    configs.append(config)
                    print(f"Loaded YAML configuration from {file_path}: {config}")
                else:
                    print(f"Empty YAML file: {file_path}")
        except yaml.YAMLError as e:
            print(f"YAML syntax error in {file_path}: {e}")
            exit(1)
        except Exception as e:
            print(f"Error loading YAML file {file_path}: {e}")
            exit(1)
    return configs

# Enhanced deep_update function to handle list merging
def deep_update(d, u):
    for k, v in u.items():
        if isinstance(v, collections.abc.Mapping):
            d[k] = deep_update(d.get(k, {}), v)
        elif isinstance(v, list):
            if k in d and isinstance(d[k], list):
                d[k] = merge_lists(d[k], v)
            else:
                d[k] = v
    return d

# Function to merge lists while removing duplicates and prioritizing higher-priority elements
def merge_lists(low_priority, high_priority):
    seen = set()
    result = []

    def item_key(item):
        if isinstance(item, dict):
            return frozenset((k, json.dumps(v, sort_keys=True)) for k, v in item.items())
        return item  

    for item in high_priority:
        key = item_key(item)
        if key not in seen:
            seen.add(key)
            result.append(item)

    for item in low_priority:
        key = item_key(item)
        if key not in seen:
            seen.add(key)
            result.append(item)

    return result

# Helper function to add metadata for visualization
def add_metadata(data, source):
    if isinstance(data, dict):
        return {k: {"value": add_metadata(v, source), "source": source} for k, v in data.items()}
    elif isinstance(data, list):
        return [{"value": add_metadata(i, source), "source": source} for i in data]
    else:
        return data

# Enhanced deep_update function to handle list merging with metadata
def deep_update_with_metadata(d, u):
    for k, v in u.items():
        if isinstance(v, collections.abc.Mapping):
            d[k] = deep_update_with_metadata(d.get(k, {}), v)
        elif isinstance(v, list):
            if k in d and isinstance(d[k], list):
                d[k] = merge_lists_with_metadata(d[k], v)
            else:
                d[k] = v
        else:
            d[k] = v
    return d

# Function to merge lists with metadata while removing duplicates and prioritizing higher-priority elements
def merge_lists_with_metadata(low_priority, high_priority):
    seen = set()
    result = []

    def item_key(item):
        if isinstance(item["value"], dict):
            return frozenset((k, json.dumps(v, sort_keys=True)) for k, v in item["value"].items())
        return item["value"]

    for item in high_priority:
        key = item_key(item)
        if key not in seen:
            seen.add(key)
            result.append(item)

    for item in low_priority:
        key = item_key(item)
        if key not in seen:
            seen.add(key)
            result.append(item)

    return result

# Helper function to print JSON with colors and proper indentation
def print_colored_json(data, indent=0):
    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict) and 'value' in value:
                color = COLORS[value["source"]]
                reset = COLORS['reset']
                print(f'{" " * indent}{color}"{key}": ', end='')
                if isinstance(value["value"], dict):
                    print('{')
                    print_colored_json(value["value"], indent + 4)
                    print(f'{" " * indent}{color}}}{reset},')# Ensure closing brace is colored
                elif isinstance(value["value"], list):
                    if all(not isinstance(item, dict) for item in value["value"]):
                        # If all items are not dicts, print in a single line  
                        print('[' + ', '.join(f'{color}{json.dumps(item)}{reset}' for item in value["value"]) + '],')
                    else:
                        print('[')
                        for item in value["value"]:
                            if isinstance(item, dict) and 'value' in item:
                                color_inner = COLORS[item["source"]]
                                reset_inner = COLORS['reset']
                                if isinstance(item["value"], dict) or isinstance(item["value"], list):
                                    print(f'{" " * (indent + 4)}{color_inner}{{')
                                    print_colored_json(item["value"], indent + 8)
                                    print(f'{" " * (indent + 4)}{color_inner}}},{reset_inner}')
                                else:
                                    print(f'{" " * (indent + 4)}{color_inner}{json.dumps(item["value"])}{reset_inner},')
                            else:
                                print(f'{" " * (indent + 4)}{json.dumps(item)},')
                        print(f'{" " * indent}]{reset},')
                else:
                    print(f'{color}{json.dumps(value["value"])}{reset},')
            else:
                if isinstance(value, dict):
                    print(f'{" " * indent}"{key}": {{')
                    print_colored_json(value, indent + 4)
                    print(f'{" " * indent}}},')
                elif isinstance(value, list):
                    if all(not isinstance(item, dict) for item in value):
                        # If all items are not dicts, print in a single line  
                        print(f'{" " * indent}"{key}": [' + ', '.join(json.dumps(item) for item in value) + '],')
                    else:
                        print(f'{" " * indent}"{key}": [')
                        for item in value:
                            if isinstance(item, dict) and 'value' in item:
                                color_inner = COLORS[item["source"]]
                                reset_inner = COLORS['reset']
                                if isinstance(item["value"], dict) or isinstance(item["value"], list):
                                    print(f'{" " * (indent + 4)}{color_inner}{{')
                                    print_colored_json(item["value"], indent + 8)
                                    print(f'{" " * (indent + 4)}{color_inner}}},{reset_inner}')
                                else:
                                    print(f'{" " * (indent + 4)}{color_inner}{json.dumps(item["value"])}{reset_inner},')
                            else:
                                print(f'{" " * (indent + 4)}{json.dumps(item)},')
                        print(f'{" " * indent}],')
                else:
                    print(f'{" " * indent}"{key}": {json.dumps(value)},')
    elif isinstance(data, list):
        if all(not isinstance(item, dict) for item in data):
            # If all items are not dicts, print in a single line  
            print('[' + ', '.join(json.dumps(item) for item in data) + '],')
        else:
            for item in data:
                if isinstance(item, dict) and 'value' in item:
                    color = COLORS[item["source"]]
                    reset = COLORS['reset']
                    if isinstance(item["value"], dict) or isinstance(item["value"], list):
                        print(f'{" " * indent}{color}{{')
                        print_colored_json(item["value"], indent + 4)
                        print(f'{" " * indent}{color}}}{reset},')
                    else:
                        print(f'{" " * indent}{color}{json.dumps(item["value"])}{reset},')
                else:
                    print(f'{" " * indent}{json.dumps(item)},')

# Paths to JSON file patterns - using specific directories for each source
override_pattern = os.path.join(workspace, 'source_repo', 'config_override', override_config_dir, '*.json')
config_pattern = os.path.join(workspace, 'config_repo', 'config', shared_config_dir, '*.json')
local_pattern = os.path.join(workspace, 'source_repo', 'config', local_config_dir, '*.json')
print(f"Override pattern: {override_pattern}")
print(f"Config pattern: {config_pattern}")
print(f"Local pattern: {local_pattern}")

# Load and validate configurations
local_configs_for_json = load_json(local_pattern, 'local')
configs_for_json = load_json(config_pattern, 'config')
override_configs_for_json = load_json(override_pattern, 'override')

# Paths to tfvars file patterns - using specific directories for each source
override_pattern_for_tfvars = os.path.join(workspace, 'source_repo', 'config_override', override_config_dir, '*.tfvars')
config_pattern_for_tfvars = os.path.join(workspace, 'config_repo', 'config', shared_config_dir, '*.tfvars')
local_pattern_for_tfvars = os.path.join(workspace, 'source_repo', 'config', local_config_dir, '*.tfvars')
print(f"Override pattern for tfvars: {override_pattern_for_tfvars}")
print(f"Config pattern for tfvars: {config_pattern_for_tfvars}")
print(f"Local pattern for tfvars: {local_pattern_for_tfvars}")

# Load and validate configurations
local_configs_for_tfvars = load_tfvars(local_pattern_for_tfvars, 'local')
configs_for_tfvars = load_tfvars(config_pattern_for_tfvars, 'config')
override_configs_for_tfvars = load_tfvars(override_pattern_for_tfvars, 'override')

# Paths to YAML file patterns - using specific directories for each source
override_pattern_for_yaml = os.path.join(workspace, 'source_repo', 'config_override', override_config_dir, '*.yaml')
override_pattern_for_yml = os.path.join(workspace, 'source_repo', 'config_override', override_config_dir, '*.yml')
config_pattern_for_yaml = os.path.join(workspace, 'config_repo', 'config', shared_config_dir, '*.yaml')
config_pattern_for_yml = os.path.join(workspace, 'config_repo', 'config', shared_config_dir, '*.yml')
local_pattern_for_yaml = os.path.join(workspace, 'source_repo', 'config', local_config_dir, '*.yaml')
local_pattern_for_yml = os.path.join(workspace, 'source_repo', 'config', local_config_dir, '*.yml')
print(f"Override pattern for yaml: {override_pattern_for_yaml}")
print(f"Override pattern for yml: {override_pattern_for_yml}")
print(f"Config pattern for yaml: {config_pattern_for_yaml}")
print(f"Config pattern for yml: {config_pattern_for_yml}")
print(f"Local pattern for yaml: {local_pattern_for_yaml}")
print(f"Local pattern for yml: {local_pattern_for_yml}")

# Load and validate YAML configurations
local_configs_for_yaml = load_yaml(local_pattern_for_yaml, 'local') + load_yaml(local_pattern_for_yml, 'local')
configs_for_yaml = load_yaml(config_pattern_for_yaml, 'config') + load_yaml(config_pattern_for_yml, 'config')
override_configs_for_yaml = load_yaml(override_pattern_for_yaml, 'override') + load_yaml(override_pattern_for_yml, 'override')

# Combine all configuration sources
local_configs = local_configs_for_json + local_configs_for_tfvars + local_configs_for_yaml
configs = configs_for_json + configs_for_tfvars + configs_for_yaml
override_configs = override_configs_for_json + override_configs_for_tfvars + override_configs_for_yaml

# Helper function to extract values from metadata structure
def extract_values_from_metadata(data):
    """Extract actual values from the metadata structure for final terraform.tfvars.json"""
    if isinstance(data, dict):
        if 'value' in data and 'source' in data:
            # This is a metadata node, extract the actual value
            return extract_values_from_metadata(data['value'])
        else:
            # This is a regular dict, process all keys
            result = {}
            for key, value in data.items():
                result[key] = extract_values_from_metadata(value)
            return result
    elif isinstance(data, list):
        result = []
        for item in data:
            if isinstance(item, dict) and 'value' in item and 'source' in item:
                # This is a metadata node, extract the actual value
                result.append(extract_values_from_metadata(item['value']))
            else:
                result.append(extract_values_from_metadata(item))
        return result
    else:
        # This is a primitive value, return as-is
        return data

# First merge local and config to get intermediate_config
intermediate_config = {}  
for config in local_configs:
    intermediate_config = deep_update(intermediate_config, config)
for config in configs:
    intermediate_config = deep_update(intermediate_config, config)

# Then merge override into intermediate_config
final_config = {}  
for config in [intermediate_config]:
    final_config = deep_update(final_config, config)
for config in override_configs:
    final_config = deep_update(final_config, config)

# Merge configurations with metadata for visualization
merged_config_with_metadata = {}  
for config, source in zip(local_configs, ['local'] * len(local_configs)):
    merged_config_with_metadata = deep_update_with_metadata(merged_config_with_metadata, add_metadata(config, source))
for config, source in zip(configs, ['config'] * len(configs)):
    merged_config_with_metadata = deep_update_with_metadata(merged_config_with_metadata, add_metadata(config, source))
for config, source in zip(override_configs, ['override'] * len(override_configs)):
    merged_config_with_metadata = deep_update_with_metadata(merged_config_with_metadata, add_metadata(config, source))

# Extract the final configuration without metadata for terraform
final_config_clean = extract_values_from_metadata(merged_config_with_metadata)

# Print the legend
print("Legend:")
print(f"{COLORS['local']}Local Config (YELLOW){COLORS['reset']}")
print(f"{COLORS['config']}Config (GREEN){COLORS['reset']}")
print(f"{COLORS['override']}Override Config (RED){COLORS['reset']}")
print("")

# Print warnings
print("WARNING: Currently merging list(objects) with deduplication are not supported!")
print("You can work this around by using set(object) data type in your terraform code.")
print("")
print("Configuration file types supported: JSON (.json), Terraform (.tfvars), YAML (.yaml/.yml)")
print("")

# Print the merged configuration with color coding in JSON format
print("Merged configuration with color coding in JSON format:")
print('{')
print_colored_json(merged_config_with_metadata, indent=4)
print('}')

# Helper function to format colored JSON for HTML - moved before generate_html_report
def format_colored_json_html(data, indent=0):
    """Format JSON exactly like print_colored_json but for HTML"""
    result = []
    indent_str = "&nbsp;" * indent

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict) and 'value' in value and 'source' in value:
                color_class = f"color-{value['source']}"
                if isinstance(value["value"], dict):
                    result.append(f'{indent_str}<span class="{color_class}">"{key}": {{</span>')
                    result.append(format_colored_json_html(value["value"], indent + 4))
                    result.append(f'{indent_str}<span class="{color_class}">}},</span>')
                elif isinstance(value["value"], list):
                    if all(not isinstance(item, dict) for item in value["value"]):
                        # Single line for simple lists
                        items_html = ', '.join(f'<span class="{color_class}">{html.escape(json.dumps(item))}</span>' for item in value["value"])
                        result.append(f'{indent_str}<span class="{color_class}">"{key}": [{items_html}],</span>')
                    else:
                        result.append(f'{indent_str}<span class="{color_class}">"{key}": [</span>')
                        for item in value["value"]:
                            if isinstance(item, dict) and 'value' in item and 'source' in item:
                                color_inner = f"color-{item['source']}"
                                if isinstance(item["value"], (dict, list)):
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">{{</span>')
                                    result.append(format_colored_json_html(item["value"], indent + 8))
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">}},</span>')
                                else:
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">{html.escape(json.dumps(item["value"]))},</span>')
                            else:
                                result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="color-normal">{html.escape(json.dumps(item))},</span>')
                        result.append(f'{indent_str}<span class="{color_class}">],</span>')
                else:
                    result.append(f'{indent_str}<span class="{color_class}">"{key}": {html.escape(json.dumps(value["value"]))},</span>')
            else:
                if isinstance(value, dict):
                    result.append(f'{indent_str}<span class="color-normal">"{key}": {{</span>')
                    result.append(format_colored_json_html(value, indent + 4))
                    result.append(f'{indent_str}<span class="color-normal">}},</span>')
                elif isinstance(value, list):
                    if all(not isinstance(item, dict) for item in value):
                        # Single line for simple lists
                        items_html = ', '.join(f'<span class="color-normal">{html.escape(json.dumps(item))}</span>' for item in value)
                        result.append(f'{indent_str}<span class="color-normal">"{key}": [{items_html}],</span>')
                    else:
                        result.append(f'{indent_str}<span class="color-normal">"{key}": [</span>')
                        for item in value:
                            if isinstance(item, dict) and 'value' in item and 'source' in item:
                                color_inner = f"color-{item['source']}"
                                if isinstance(item["value"], (dict, list)):
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">{{</span>')
                                    result.append(format_colored_json_html(item["value"], indent + 8))
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">}},</span>')
                                else:
                                    result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="{color_inner}">{html.escape(json.dumps(item["value"]))},</span>')
                            else:
                                result.append(f'{indent_str}&nbsp;&nbsp;&nbsp;&nbsp;<span class="color-normal">{html.escape(json.dumps(item))},</span>')
                        result.append(f'{indent_str}<span class="color-normal">],</span>')
                else:
                    result.append(f'{indent_str}<span class="color-normal">"{key}": {html.escape(json.dumps(value))},</span>')
    elif isinstance(data, list):
        if all(not isinstance(item, dict) for item in data):
            # Single line for simple lists
            items_html = ', '.join(f'<span class="color-normal">{html.escape(json.dumps(item))}</span>' for item in data)
            result.append(f'[{items_html}],')
        else:
            for item in data:
                if isinstance(item, dict) and 'value' in item and 'source' in item:
                    color = f"color-{item['source']}"
                    if isinstance(item["value"], (dict, list)):
                        result.append(f'{indent_str}<span class="{color}">{{</span>')
                        result.append(format_colored_json_html(item["value"], indent + 4))
                        result.append(f'{indent_str}<span class="{color}">}},</span>')
                    else:
                        result.append(f'{indent_str}<span class="{color}">{html.escape(json.dumps(item["value"]))},</span>')
                else:
                    result.append(f'{indent_str}<span class="color-normal">{html.escape(json.dumps(item))},</span>')

    return '\n'.join(result)

# Function to generate HTML report for config merge results
def generate_html_report(merged_config_with_metadata, final_config, workspace):
    # Get Azure DevOps pipeline information
    def get_pipeline_metadata():
        """Collect pipeline metadata from environment variables"""
        metadata = {
            'build_number': os.getenv('BUILD_BUILDNUMBER', 'N/A'),
            'build_id': os.getenv('BUILD_BUILDID', 'N/A'),
            'pipeline_name': os.getenv('BUILD_DEFINITIONNAME', 'N/A'),
            'triggered_by': os.getenv('BUILD_QUEUEDBY', 'N/A'),
            'triggered_by_email': os.getenv('BUILD_QUEUEDBYID', 'N/A'),
            'source_branch': os.getenv('BUILD_SOURCEBRANCH', 'N/A'),
            'source_version': os.getenv('BUILD_SOURCEVERSION', 'N/A')[:8] if os.getenv('BUILD_SOURCEVERSION') else 'N/A',
            'start_time': os.getenv('SYSTEM_PIPELINESTARTTIME', 'N/A'),
            'team_project': os.getenv('SYSTEM_TEAMPROJECT', 'N/A'),
            'collection_uri': os.getenv('SYSTEM_TEAMFOUNDATIONCOLLECTIONURI', ''),
            'agent_name': os.getenv('AGENT_NAME', 'N/A'),
            'repository_name': os.getenv('BUILD_REPOSITORY_NAME', 'N/A'),
            'repository_uri': os.getenv('BUILD_REPOSITORY_URI', ''),
        }
        
        # Construct pipeline URL
        if metadata['collection_uri'] and metadata['team_project'] and metadata['build_id']:
            base_uri = metadata['collection_uri'].rstrip('/')
            metadata['pipeline_url'] = f"{base_uri}/{metadata['team_project']}/_build/results?buildId={metadata['build_id']}"
        else:
            metadata['pipeline_url'] = '#'
            
        # Format branch name (remove refs/heads/ prefix)
        if metadata['source_branch'].startswith('refs/heads/'):
            metadata['source_branch'] = metadata['source_branch'][11:]
            
        # Format start time
        if metadata['start_time'] != 'N/A':
            try:
                from datetime import datetime
                start_dt = datetime.fromisoformat(metadata['start_time'].replace('Z', '+00:00'))
                metadata['start_time_formatted'] = start_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            except:
                metadata['start_time_formatted'] = metadata['start_time']
        else:
            metadata['start_time_formatted'] = 'N/A'
            
        return metadata

    # Collect original files information
    def collect_original_files():
        original_files = {
            'local': {'json': [], 'tfvars': [], 'yaml': []},
            'config': {'json': [], 'tfvars': [], 'yaml': []},
            'override': {'json': [], 'tfvars': [], 'yaml': []}
        }

        # JSON files
        for file_path in glob(local_pattern, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['local']['json'].append({'path': file_path, 'content': content})

        for file_path in glob(config_pattern, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['config']['json'].append({'path': file_path, 'content': content})

        for file_path in glob(override_pattern, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['override']['json'].append({'path': file_path, 'content': content})

        # TFVars files
        for file_path in glob(local_pattern_for_tfvars, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['local']['tfvars'].append({'path': file_path, 'content': content})

        for file_path in glob(config_pattern_for_tfvars, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['config']['tfvars'].append({'path': file_path, 'content': content})

        for file_path in glob(override_pattern_for_tfvars, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['override']['tfvars'].append({'path': file_path, 'content': content})

        # YAML files
        for file_path in glob(local_pattern_for_yaml, recursive=True) + glob(local_pattern_for_yml, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['local']['yaml'].append({'path': file_path, 'content': content})

        for file_path in glob(config_pattern_for_yaml, recursive=True) + glob(config_pattern_for_yml, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['config']['yaml'].append({'path': file_path, 'content': content})

        for file_path in glob(override_pattern_for_yaml, recursive=True) + glob(override_pattern_for_yml, recursive=True):
            with open(file_path, 'r') as f:
                content = f.read()
            original_files['override']['yaml'].append({'path': file_path, 'content': content})

        return original_files

    # Generate pipeline metadata section
    def generate_metadata_section():
        metadata = get_pipeline_metadata()
        
        # Count configuration files found and get file lists with links
        def get_file_list_with_links(source_type, source_id):
            files = []
            for file_info in original_files[source_type]['json']:
                file_name = os.path.basename(file_info['path'])
                files.append(f'<a href="#{source_id}" class="file-link">{file_name}</a>')
            for file_info in original_files[source_type]['tfvars']:
                file_name = os.path.basename(file_info['path'])
                files.append(f'<a href="#{source_id}" class="file-link">{file_name}</a>')
            for file_info in original_files[source_type]['yaml']:
                file_name = os.path.basename(file_info['path'])
                files.append(f'<a href="#{source_id}" class="file-link">{file_name}</a>')
            return '<br>'.join(files) if files else 'No files'
        
        local_files = get_file_list_with_links('local', 'local-files')
        config_files = get_file_list_with_links('config', 'config-files')
        override_files = get_file_list_with_links('override', 'override-files')
        
        # Get repository URLs and versions from environment variables
        collection_uri = metadata['collection_uri'].rstrip('/')
        
        # Get additional repository information from environment variables
        pipelines_version = os.getenv('BUILD_SOURCEVERSION_PIPELINES', 'N/A')
        pipelines_version_short = pipelines_version[:8] if pipelines_version != 'N/A' else 'N/A'
        pipelinetemplates_version = os.getenv('BUILD_SOURCEVERSION_PIPELINETEMPLATES', 'N/A') 
        pipelinetemplates_version_short = pipelinetemplates_version[:8] if pipelinetemplates_version != 'N/A' else 'N/A'
        config_version = os.getenv('BUILD_SOURCEVERSION_CONFIG', 'N/A')
        config_version_short = config_version[:8] if config_version != 'N/A' else 'N/A'
        source_version_full = os.getenv('BUILD_SOURCEVERSION', 'N/A');
        
        # Construct repository URLs with correct project paths
        def get_repo_url(project, repo_name, branch_or_tag):
            # Handle special case where repo_name contains project/repo format
            if '/' in repo_name:
                # repo_name is in format "project/repo", use the project from repo_name
                repo_project = repo_name.split('/')[0]
                actual_repo = repo_name.split('/')[1]
                if 'refs/tags/' in branch_or_tag:
                    tag = branch_or_tag.replace('refs/tags/', '')
                    return f"{collection_uri}/{repo_project}/_git/{actual_repo}?version=GT{tag}"
                else:
                    return f"{collection_uri}/{repo_project}/_git/{actual_repo}?version=GB{branch_or_tag}"
            else:
                if 'refs/tags/' in branch_or_tag:
                    tag = branch_or_tag.replace('refs/tags/', '')
                    return f"{collection_uri}/{project}/_git/{repo_name}?version=GT{tag}"
                else:
                    return f"{collection_uri}/{project}/_git/{repo_name}?version=GB{branch_or_tag}"
        
        # Get commit URLs for versions where available
        def get_commit_url(project, repo_name, commit_hash):
            if commit_hash != 'N/A' and len(commit_hash) >= 8:
                # Handle special case where repo_name contains project/repo format
                if '/' in repo_name:
                    repo_project = repo_name.split('/')[0]
                    actual_repo = repo_name.split('/')[1]
                    return f"{collection_uri}/{repo_project}/_git/{actual_repo}/commit/{commit_hash}"
                else:
                    return f"{collection_uri}/{project}/_git/{repo_name}/commit/{commit_hash}"
            return None
        
        # Repository URLs
        pipelines_url = get_repo_url('gcp-lz', 'pipelines', 'config-step')
        pipelinetemplates_url = get_repo_url('gcp-lz', 'OTPHU-CDO-ADOS-TOOLS/pipelinetemplates', 'refs/tags/v7')
        source_repo_url = get_repo_url('gcp-lz', metadata['repository_name'], metadata['source_branch'])
        config_repo_url = get_repo_url('gcp-lz', 'terraform-config-lztest-projects', 'env-configs')
        
        # Commit URLs
        pipelines_commit_url = get_commit_url('gcp-lz', 'pipelines', pipelines_version)
        pipelinetemplates_commit_url = get_commit_url('gcp-lz', 'OTPHU-CDO-ADOS-TOOLS/pipelinetemplates', pipelinetemplates_version)
        source_commit_url = get_commit_url('gcp-lz', metadata['repository_name'], source_version_full)
        config_commit_url = get_commit_url('gcp-lz', 'terraform-config-lztest-projects', config_version)
        
        metadata_html = f"""
        <div class="metadata-section">
            <h2>📊 Pipeline Information</h2>
            <div class="metadata-grid">
                <div class="metadata-card pipeline-details-card">
                    <h3>🚀 Pipeline Details</h3>
                    <table class="metadata-table">
                        <tr><td><strong>Pipeline:</strong></td><td><a href="{metadata['pipeline_url']}" target="_blank">{metadata['pipeline_name']}</a></td></tr>
                        <tr><td><strong>Triggered by:</strong></td><td>{metadata['triggered_by']}</td></tr>
                        <tr><td><strong>Build Number:</strong></td><td>{metadata['build_number']}</td></tr>
                        <tr><td><strong>Build ID:</strong></td><td>{metadata['build_id']}</td></tr>
                        <tr><td><strong>Started:</strong></td><td>{metadata['start_time_formatted']}</td></tr>
                        <tr><td><strong>Agent:</strong></td><td>{metadata['agent_name']}</td></tr>
                    </table>
                </div>
                
                <div class="metadata-card sources-card">
                    <h3>📁 Sources</h3>
                    <table class="sources-table">
                        <thead>
                            <tr>
                                <th>Repository</th>
                                <th>Branch/Tag</th>
                                <th>Version</th>
                                <th>Path</th>
                                <th>Files</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>pipelines</strong></td>
                                <td><a href="{pipelines_url}" target="_blank" class="branch-name">config-step</a></td>
                                <td>{"<a href='" + pipelines_commit_url + "' target='_blank' class='commit-hash'>" + pipelines_version_short + "</a>" if pipelines_commit_url else "<span class='commit-hash'>" + pipelines_version_short + "</span>"}</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td><strong>pipelinetemplates</strong></td>
                                <td><a href="{pipelinetemplates_url}" target="_blank" class="branch-name">refs/tags/v7</a></td>
                                <td>{"<a href='" + pipelinetemplates_commit_url + "' target='_blank' class='commit-hash'>" + pipelinetemplates_version_short + "</a>" if pipelinetemplates_commit_url else "<span class='commit-hash'>" + pipelinetemplates_version_short + "</span>"}</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td rowspan="2"><strong>{metadata['repository_name']}</strong> <span class="source-type">(source)</span></td>
                                <td rowspan="2"><a href="{source_repo_url}" target="_blank" class="branch-name">{metadata['source_branch']}</a></td>
                                <td rowspan="2">{"<a href='" + source_commit_url + "' target='_blank' class='commit-hash'>" + metadata['source_version'] + "</a>" if source_commit_url else "<span class='commit-hash'>" + metadata['source_version'] + "</span>"}</td>
                                <td><code>config/{local_config_dir or 'Not set'}</code></td>
                                <td><div class="file-list">{local_files}</div></td>
                            </tr>
                            <tr>
                                <td><code>config_override/{override_config_dir or 'Not set'}</code></td>
                                <td><div class="file-list">{override_files}</div></td>
                            </tr>
                            <tr>
                                <td><strong>terraform-config-lztest-projects</strong> <span class="source-type">(config)</span></td>
                                <td><a href="{config_repo_url}" target="_blank" class="branch-name">env-configs</a></td>
                                <td>{"<a href='" + config_commit_url + "' target='_blank' class='commit-hash'>" + config_version_short + "</a>" if config_commit_url else "<span class='commit-hash'>" + config_version_short + "</span>"}</td>
                                <td><code>config/{shared_config_dir or 'Not set'}</code></td>
                                <td><div class="file-list">{config_files}</div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        """
        return metadata_html

    # Collect original files information
    original_files = collect_original_files()

    def generate_file_sections():
        sections_html = ""
        
        source_info = {
            'local': {'title': '🟡 Local Config Files', 'desc': 'Configuration files from source repository', 'id': 'local-files'},
            'config': {'title': '🟢 Shared Config Files', 'desc': 'Configuration files from config repository', 'id': 'config-files'},
            'override': {'title': '🔴 Override Config Files', 'desc': 'Override configuration files from source repository', 'id': 'override-files'}
        }
        
        def get_language_class(file_path):
            """Determine the language class for syntax highlighting based on file extension"""
            if file_path.endswith('.json'):
                return 'language-json'
            elif file_path.endswith('.tfvars'):
                return 'language-hcl'
            elif file_path.endswith(('.yaml', '.yml')):
                return 'language-yaml'
            else:
                return 'language-text'
        
        # Start the main container with specific class for vertical layout
        sections_html += """
        <div class="metadata-section original-config-files">
            <h2>📁 Original Configuration Files</h2>
            <div class="metadata-grid">
        """
        
        for source_type, source_data in source_info.items():
            if any(original_files[source_type][fmt] for fmt in ['json', 'tfvars', 'yaml']):
                sections_html += f"""
                <div class="metadata-card">
                    <h3 id="{source_data['id']}">{source_data['title']}</h3>
                    <p class="source-description">{source_data['desc']}</p>
                """
                
                # JSON files
                if original_files[source_type]['json']:
                    sections_html += f"""
                    <h4>📄 JSON Files</h4>
                    """
                    for file_info in original_files[source_type]['json']:
                        file_name = os.path.basename(file_info['path'])
                        lang_class = get_language_class(file_info['path'])
                        sections_html += f"""
                    <div class="file-section">
                        <h5 class="file-title">📁 {file_name}</h5>
                        <div class="file-path">{file_info['path']}</div>
                        <div class="code-block">
                            <pre><code class="{lang_class}">{html.escape(file_info['content'])}</code></pre>
                        </div>
                    </div>
                    """
                
                # TFVars files
                if original_files[source_type]['tfvars']:
                    sections_html += f"""
                    <h4>⚙️ Terraform Variables Files</h4>
                    """
                    for file_info in original_files[source_type]['tfvars']:
                        file_name = os.path.basename(file_info['path'])
                        lang_class = get_language_class(file_info['path'])
                        sections_html += f"""
                    <div class="file-section">
                        <h5 class="file-title">📁 {file_name}</h5>
                        <div class="file-path">{file_info['path']}</div>
                        <div class="code-block">
                            <pre><code class="{lang_class}">{html.escape(file_info['content'])}</code></pre>
                        </div>
                    </div>
                    """
                
                # YAML files
                if original_files[source_type]['yaml']:
                    sections_html += f"""
                    <h4>📋 YAML Files</h4>
                    """
                    for file_info in original_files[source_type]['yaml']:
                        file_name = os.path.basename(file_info['path'])
                        lang_class = get_language_class(file_info['path'])
                        sections_html += f"""
                    <div class="file-section">
                        <h5 class="file-title">📁 {file_name}</h5>
                        <div class="file-path">{file_info['path']}</div>
                        <div class="code-block">
                            <pre><code class="{lang_class}">{html.escape(file_info['content'])}</code></pre>
                        </div>
                    </div>
                    """
                
                sections_html += """
                </div>
                """
        
        # Close the main container
        sections_html += """
            </div>
        </div>
        """
        
        return sections_html

    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Configuration Merge Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/yaml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/javascript.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #0078d4;
            padding-bottom: 10px;
        }
        h2 {
            color: #666;
            margin-top: 40px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        h3 {
            color: #495057;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h4 {
            color: #6c757d;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .legend {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .legend-item {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .local { background-color: #fff3cd; color: #856404; }
        .config { background-color: #d4edda; color: #155724; }
        .override { background-color: #f8d7da; color: #721c24; }
        .code-block {
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            padding: 15px;
            background-color: #1e1e1e;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            overflow-x: auto;
        }
        .code-block pre code {
            background: transparent;
            padding: 0;
            border: none;
            font-size: inherit;
        }
        .priority-info {
            background-color: #e7f3ff;
            border-left: 4px solid #0078d4;
            padding: 10px;
            margin: 20px 0;
        }
        .source-description {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 20px;
        }
        .file-section {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 15px 0;
            background-color: #fafafa;
        }
        .file-title {
            background-color: #e9ecef;
            margin: 0;
            padding: 10px 15px;
            border-radius: 5px 5px 0 0;
            border-bottom: 1px solid #dee2e6;
        }
        .file-path {
            background-color: #f8f9fa;
            padding: 8px 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
        }
        .file-section .code-block {
            margin: 0;
            border: none;
            border-radius: 0 0 5px 5px;
        }
        
        /* Metadata section styles */
        .metadata-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .metadata-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .metadata-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #495057;
            font-size: 16px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 8px;
        }
        
        .metadata-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .metadata-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: top;
        }
        
        .metadata-table td:first-child {
            width: 40%;
            color: #6c757d;
        }
        
        .metadata-table td:last-child {
            color: #495057;
        }
        
        .branch-name {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
        }
        
        .commit-hash {
            background-color: #f3e5f5;
            color: #7b1fa2;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', monospace;
            font-size: 12px;
        }
        
        .metadata-table code {
            background-color: #f8f9fa;
            color: #e83e8c;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        /* Console-like color styling for merged config */
        .merged-config .color-local { color: #ffff00; font-weight: bold; }  /* Bright Yellow */
        .merged-config .color-config { color: #00ff00; font-weight: bold; } /* Bright Green */
        .merged-config .color-override { color: #ff0000; font-weight: bold; } /* Bright Red */
        .merged-config .color-normal { color: #d4d4d4; }
        
        /* Table of Contents */
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .toc ul {
            margin: 0;
            padding-left: 20px;
        }
        .toc a {
            text-decoration: none;
            color: #0078d4;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        
        /* Sources table specific styles */
        .sources-card {
            grid-column: 1 / -1; /* Span full width */
        }
        
        /* Pipeline Details card - make it span full width like Sources */
        .pipeline-details-card {
            grid-column: 1 / -1; /* Span full width */
        }
        
        .sources-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .sources-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: bold;
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            text-align: left;
            font-size: 13px;
        }
        
        .sources-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            vertical-align: top;
            font-size: 13px;
        }
        
        .sources-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        /* Special styling for rowspan cells */
        .sources-table td[rowspan] {
            vertical-align: middle;
            background-color: #f1f3f4;
        }
        
        .source-type {
            font-size: 12px;
            color: #6c757d;
            font-weight: normal;
        }
        
        .file-list {
            font-size: 12px;
            line-height: 1.6;
        }
        
        .file-link {
            color: #0078d4;
            text-decoration: none;
        }
        
        .file-link:hover {
            text-decoration: underline;
        }
        
        .branch-name a, .commit-hash a {
            color: inherit;
            text-decoration: none;
        }
        
        .branch-name a:hover, .commit-hash a:hover {
            text-decoration: underline;
        }
        
        /* Additional styles for file sections */
        .file-section {
            margin-bottom: 25px;
        }
        
        .file-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .file-path {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        /* File section styling inside metadata cards */
        .metadata-card .file-section {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 10px 0;
            background-color: #ffffff;
        }
        
        .metadata-card .file-title {
            background-color: #f8f9fa;
            margin: 0;
            padding: 8px 12px;
            border-radius: 5px 5px 0 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 13px;
        }
        
        .metadata-card .file-path {
            background-color: #f8f9fa;
            padding: 6px 12px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
        }
        
        .metadata-card .code-block {
            margin: 0;
            border: none;
            border-radius: 0 0 5px 5px;
        }
        
        .metadata-card h4 {
            color: #6c757d;
            font-size: 14px;
            margin-top: 15px;
            margin-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }
        
        .metadata-card h5 {
            color: #6c757d;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        
        /* Override grid layout for Original Configuration Files section */
        .original-config-files .metadata-grid {
            display: block; /* Change from grid to block layout */
        }
        
        .original-config-files .metadata-card {
            margin-bottom: 30px; /* Add space between vertical cards */
            width: 100%; /* Ensure full width */
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Configuration Merge Report</h1>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#pipeline-info">Pipeline Information</a></li>
                <li><a href="#legend">Legend & Priority</a></li>
                <li><a href="#merged-config">Final Merged Configuration</a></li>
                <li><a href="#original-files">Original Configuration Files</a>
                    <ul>
                        <li><a href="#local-files">🟡 Local Config Files</a></li>
                        <li><a href="#config-files">🟢 Shared Config Files</a></li>
                        <li><a href="#override-files">🔴 Override Config Files</a></li>
                    </ul>
                </li>
            </ul>
        </div>

        <div id="pipeline-info">
""" + generate_metadata_section() + """
        </div>

        <div id="legend" class="legend">
            <h3>📋 Legend:</h3>
            <div class="legend-item local">🟡 <a href="#local-files">Local Config</a></div> - Configuration from source repository<br>
            <div class="legend-item config">🟢 <a href="#config-files">Shared Config</a></div> - Configuration from config repository<br>
            <div class="legend-item override">🔴 <a href="#override-files">Override Config</a></div> - Override configuration from source repository
            
            <div class="priority-info">
                <strong>⚡ Priority Order:</strong> <a href="#local-files">Local Config</a> &lt; <a href="#config-files">Shared Config</a> &lt; <a href="#override-files">Override Config</a><br>
                Override configs have the highest priority and will overwrite conflicting values.<br>
                <strong>📁 Supported file types:</strong> JSON (.json), Terraform (.tfvars), YAML (.yaml/.yml)
            </div>
        </div>

        <div id="merged-config" class="legend">
            <h3>🎯 Final Merged Configuration</h3>
            <p>This shows the final configuration with color coding indicating the source of each value:</p>
            <div class="code-block merged-config">
                <pre>""" + format_colored_json_html(merged_config_with_metadata) + """</pre>
            </div>
        </div>

        <div id="original-files">
""" + generate_file_sections() + """
        </div>
    </div>
    
    <script>
        // Initialize syntax highlighting
        hljs.highlightAll();
        
        // For .tfvars files, if highlighting fails, fallback to JavaScript highlighting
        document.querySelectorAll('code.language-hcl').forEach(function(block) {
            // If the block doesn't have proper highlighting (less than 3 hljs classes)
            const highlightedElements = block.querySelectorAll('[class*="hljs-"]');
            if (highlightedElements.length < 3) {
                // Remove the hcl class and add javascript for better highlighting
                block.className = block.className.replace('language-hcl', 'language-javascript');
                // Re-highlight with JavaScript highlighting
                hljs.highlightElement(block);
            }
        });
        
        // Add copy buttons to code blocks
        document.querySelectorAll('.file-section .code-block pre').forEach(function(block) {
            var button = document.createElement('button');
            button.className = 'copy-btn';
            button.textContent = 'Copy';
            button.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #007acc; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px; z-index: 10;';
            
            var container = block.parentElement;
            container.style.position = 'relative';
            container.appendChild(button);
            
            button.addEventListener('click', function() {
                var code = block.querySelector('code');
                const text = code.textContent || code.innerText;
                navigator.clipboard.writeText(text).then(function() {
                    button.textContent = 'Copied!';
                    setTimeout(function() {
                        button.textContent = 'Copy';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
"""

    # Write HTML file with error handling
    html_file_path = os.path.join(workspace, 'config-merge-report.html')
    with open(html_file_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    return html_file_path

# Helper function to generate markdown summary - moved outside of generate_html_report
def generate_markdown_summary(merged_config_with_metadata, workspace):
    """Generate a markdown summary for Azure DevOps pipeline summary"""

    # Get Azure DevOps variables for constructing the download URL
    system_team_foundation_collection_uri = os.getenv('SYSTEM_TEAMFOUNDATIONCOLLECTIONURI', '')
    system_team_project = os.getenv('SYSTEM_TEAMPROJECT', '')
    build_build_id = os.getenv('BUILD_BUILDID', '')

    # Construct the artifact download URL
    if all([system_team_foundation_collection_uri, system_team_project, build_build_id]):
        # Remove trailing slash from collection URI if present
        base_uri = system_team_foundation_collection_uri.rstrip('/')

        # Use the proper Azure DevOps REST API format for artifact download
        # Format: {collection_uri}/{project}/_apis/build/builds/{buildId}/artifacts?artifactName={artifactName}&api-version=6.0&$format=zip
        artifact_download_url = f"{base_uri}/{system_team_project}/_apis/build/builds/{build_build_id}/artifacts?artifactName=ConfigMergeReport&api-version=6.0&$format=zip"

        # Also provide the web UI URL as an alternative
        artifact_web_url = f"{base_uri}/{system_team_project}/_build/results?buildId={build_build_id}&view=artifacts&pathAsName=false&type=publishedArtifacts"

        artifact_link_text = f"📄 [Download HTML Report (Direct)]({artifact_download_url}) | [View in Browser]({artifact_web_url})"
    else:
        artifact_link_text = "📄 Download the **ConfigMergeReport** artifact from the pipeline artifacts"

    def format_json_for_markdown(data, indent=0):
        """Format JSON for markdown with source indicators"""
        result = []
        indent_str = "  " * indent

        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, dict) and 'value' in value and 'source' in value:
                    source = value['source']
                    source_emoji = {"local": "🟡", "config": "🟢", "override": "🔴"}[source]

                    if isinstance(value["value"], dict):
                        result.append(f'{indent_str}**{source_emoji} "{key}"**: {{')
                        result.append(format_json_for_markdown(value["value"], indent + 1))
                        result.append(f'{indent_str}}}')
                    elif isinstance(value["value"], list):
                        items_str = json.dumps(value["value"])
                        result.append(f'{indent_str}**{source_emoji} "{key}"**: `{items_str}`')
                    else:
                        result.append(f'{indent_str}**{source_emoji} "{key}"**: `{json.dumps(value["value"])}`')
                else:
                    if isinstance(value, (dict, list)):
                        result.append(f'{indent_str}"{key}": {{')
                        result.append(format_json_for_markdown(value, indent + 1))
                        result.append(f'{indent_str}}}')
                    else:
                        result.append(f'{indent_str}"{key}": `{json.dumps(value)}`')

        return '\n'.join(result)

    markdown_content = f"""# 🔧 Configuration Merge Report

## 📋 Legend
- 🟡 **Local Config** - Configuration from source repository
- 🟢 **Shared Config** - Configuration from config repository  
- 🔴 **Override Config** - Override configuration from source repository

## ⚡ Priority Order
**Local Config < Shared Config < Override Config**

Override configs have the highest priority and will overwrite conflicting values.

## 🎯 Final Merged Configuration

```json
{json.dumps(final_config_clean, indent=2)}
```

## 📄 Download Detailed Report

{artifact_link_text}

The HTML report provides the same color-coded view as shown in the pipeline console output, with:
- **Yellow highlighting** for Local Config values
- **Green highlighting** for Shared Config values
- **Red highlighting** for Override Config values

### Alternative access methods:
1. **Direct download**: Click the "Download HTML Report (Direct)" link above
2. **Pipeline artifacts**: Go to this pipeline run's artifacts section
3. **Manual download**: Look for the **ConfigMergeReport** artifact in the pipeline summary

---

*Generated automatically by the Merge Configurations job*
"""

    # Write markdown file with error handling
    try:
        markdown_file_path = os.path.join(workspace, 'config-merge-summary.md')
        with open(markdown_file_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        return markdown_file_path
    except Exception as e:
        print(f"Error writing markdown file: {e}")
        # Return a dummy path so the script continues
        return os.path.join(workspace, 'config-merge-summary.md')

# Generate HTML report
html_report_path = generate_html_report(merged_config_with_metadata, final_config_clean, workspace)
print(f"HTML report generated: {html_report_path}")

# Generate Markdown summary
markdown_summary_path = generate_markdown_summary(merged_config_with_metadata, workspace)
print(f"Markdown summary generated: {markdown_summary_path}")

# Write final terraform.tfvars.json file
tfvars_file_path = os.path.join(workspace, 'terraform.tfvars.json')
with open(tfvars_file_path, 'w', encoding='utf-8') as f:
    json.dump(final_config_clean, f, indent=4)
print(f"Final terraform.tfvars.json file written: {tfvars_file_path}")
