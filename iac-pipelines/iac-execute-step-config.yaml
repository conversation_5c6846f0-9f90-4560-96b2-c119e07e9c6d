parameters:
  # Terraform configuration
  - name: config_directory
    default: ''
  - name: config_directory_dev
    default: ''
  - name: config_directory_prod
    default: ''
  - name: config_directory_override
    default: ''
  - name: terraformProjectLocation
    default: ''
  - name: isInitStage
    type: boolean
    default: true

  # Checkouts
  - name: config_repo
    type: string
  - name: pipelines_repo
    type: string

### Config steps
steps:
  # Merge Configs - only in init stage
  - ${{ if parameters.isInitStage }}:
    - script: |
        python3 --version
        python3 -m venv venv
        source venv/bin/activate
        pip install --no-cache-dir --upgrade pip
        pip install --no-cache-dir python-hcl2 rich PyYAML
        CONFIG_DIRECTORY='${{ parameters.config_directory }}' \
        CONFIG_DIRECTORY_DEV='${{ parameters.config_directory_dev }}' \
        CONFIG_DIRECTORY_PROD='${{ parameters.config_directory_prod }}' \
        CONFIG_DIRECTORY_OVERRIDE='${{ parameters.config_directory_override }}' \
        WORKSPACE='$(Pipeline.Workspace)' \
        python merge_configs.py
      workingDirectory: $(Pipeline.Workspace)/pipelines_repo/scripts
      displayName: 'Merge Configurations'

    - script: |
        echo "##vso[task.uploadsummary]$(Pipeline.Workspace)/config-merge-summary.md"
      displayName: 'Upload Configuration Summary to Pipeline'
      condition: always()

    - task: PublishPipelineArtifact@1
      displayName: 'Publish Config Merge HTML Report'
      condition: always()
      inputs:
        targetPath: $(Pipeline.Workspace)/config-merge-report.html
        artifact: ConfigMergeReport
        publishLocation: pipeline

    - task: PublishBuildArtifacts@1
      inputs:
        PathtoPublish: $(Pipeline.Workspace)/terraform.tfvars.json
        ArtifactName: terraform-tfvars
      displayName: 'Upload terraform.tfvars.json as artifact'

    # Copy terraform.tfvars.json to terraform project location
    - task: CopyFiles@2
      displayName: 'Copy terraform.tfvars.json to terraform project location'
      inputs:
        SourceFolder: $(Pipeline.Workspace)
        Contents: terraform.tfvars.json
        TargetFolder: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}

  # Download config artifact - only in deployment stage
  - ${{ if eq(parameters.isInitStage, false) }}:
    - task: DownloadPipelineArtifact@2
      displayName: Download terraform.tfvars.json
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}
        artifact: terraform-tfvars
        patterns: terraform.tfvars.json