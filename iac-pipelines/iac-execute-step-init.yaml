parameters:
  # GIT Prepare
  - name: tfModulesAZDOProject
    default: OTPHU-COE-TEMPLATESPEC

  # Config parameters
  - name: config_directory
    default: ''
  - name: config_directory_dev
    default: ''
  - name: config_directory_prod
    default: ''
  - name: config_directory_override
    default: ''
  - name: config_repo
    default: ''
  - name: runConfigStep
    type: boolean
    default: false
  - name: isInitStage
    type: boolean
    default: true
  - name: fabric
    type: boolean
    default: false

  # GCP Auth
  - name: googleCloudProject
  - name: googleCloudRegion
  - name: googleCloudKeyFile
  - name: googleImpersonateServiceAccount

  # ADO Auth
  # - name: azureDevOpsOrgServiceURL

  # Azure Auth
  - name: armServiceConnection
    default: ''
  - name: subscriptionID
    default: ''

  # GCP Backend
  - name: googleBucketName
  - name: terraformStateFilePath
    # default: /

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformVersion
  - name: terraformExtraNoProxy
  - name: terraformRCFileForNetworkMirror
  - name: terraformUnlockStateLockID
    default: ''

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForInit
  - name: terraformCLIOptionsForInit

  # Not exposed options
  - name: publishArtifact
    default: false

### Init steps
steps:

  # Azure Login
  - ${{ if ne(parameters.armServiceConnection, '') }}:
    - template: ../common/step-azure-oidc-login.yaml
      parameters:
        armServiceConnection: ${{ parameters.armServiceConnection }}
        subscriptionID: ${{ parameters.subscriptionID }}

  # GIT insteadOf configuration
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/step-tf-git-prepare.yaml
  - template: iac-common/step-tf-git-prepare.yaml@pipelinetemplates
    parameters:
      terraformModulesAccessPAT: $(TERRAFORM_MODULES_ACCESS_PAT)
      tfModulesAZDOProject: ${{ parameters.tfModulesAZDOProject }}

  # Checkouts - consolidated for all repos
  - checkout: self
    displayName: 'Checkout Infra Repository'
    path: 'source_repo'

  - ${{ if and(parameters.runConfigStep, ne(parameters.config_repo, '')) }}:
    - checkout: ${{ parameters.config_repo }}
      displayName: 'Checkout Config Repository'
      path: 'config_repo'

  - checkout: pipelines
    displayName: 'Checkout Pipelines Repository'
    path: 'pipelines_repo'

  # Config step - run when enabled
  - ${{ if parameters.runConfigStep }}:
    - template: iac-execute-step-config.yaml
      parameters:
        config_directory: ${{ parameters.config_directory }}
        config_directory_dev: ${{ parameters.config_directory_dev }}
        config_directory_prod: ${{ parameters.config_directory_prod }}
        config_directory_override: ${{ parameters.config_directory_override }}
        config_repo: ${{ parameters.config_repo }}
        pipelines_repo: pipelines
        terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
        isInitStage: ${{ parameters.isInitStage }}

  # Download lock artifact for deploy stage (publishArtifact=false)
  - ${{ if eq(parameters.publishArtifact, false) }}:
    - task: DownloadPipelineArtifact@2
      displayName: Download terraform lock file
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}
        artifact: tflock

  # Copy .terraformrc to user home
  - template: iac-generic-step-terraformrc.yaml
    parameters:
      terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
      terraformVersion: ${{ parameters.terraformVersion }}

  # Download GCP Key File
  - task: DownloadSecureFile@1
    name: downloadGCPKeyFile
    displayName: Download GCP Key File
    inputs:
      secureFile: ${{ parameters.googleCloudKeyFile }}

  # Set GCP Key File Permissions
  - task: Bash@3
    displayName: Set GCP Key File Permissions
    inputs:
      targetType: 'inline'
      script: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR
        chmod 600 $(downloadGCPKeyFile.secureFilePath)

  # Terraform Init
  - task: Bash@3
    displayName: Terraform init
    name: prepare
    env:
      GOOGLE_PROJECT: ${{ parameters.googleCloudProject }}
      GOOGLE_REGION: ${{ parameters.googleCloudRegion }}
      GOOGLE_CREDENTIALS: $(downloadGCPKeyFile.secureFilePath)
      GOOGLE_IMPERSONATE_SERVICE_ACCOUNT: ${{ parameters.googleImpersonateServiceAccount }}
      # AZDO_PERSONAL_ACCESS_TOKEN : $(System.AccessToken)
      # AZDO_ORG_SERVICE_URL: ${{ parameters.azureDevOpsOrgServiceURL }}
      ARM_USE_OIDC: true
      ARM_OIDC_TOKEN: $(ARM_OIDC_TOKEN)
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      script: |
        set -eu  # fail on error
        trap "echo Error on line $LINENO" ERR

        export TERRAFORMVERSION=${{ parameters.terraformVersion }}
        export TERRAFORM_VERSION=${{ parameters.terraformVersion }}
        NO_PROXY_TEMP=$NO_PROXY,${{ parameters.terraformExtraNoProxy }}
        export NO_PROXY=$(echo $NO_PROXY_TEMP | sed "s/,\*.core.windows.net//" | sed "s/,vault.azure.net//")
        export no_proxy=$NO_PROXY
        echo "All environment variables:"
        env | sort
        echo "##vso[task.setvariable variable=NO_PROXY]${NO_PROXY}"
        echo "##vso[task.setvariable variable=no_proxy]${no_proxy}"

        echo "Initializing terraform"
        terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} init \
          -backend-config="bucket=${{ parameters.googleBucketName }}" \
          -backend-config="prefix=${{ parameters.terraformStateFilePath }}" ${{ parameters.terraformCLIOptionsForInit }}

  # Unlock statefile
  - ${{ if ne(parameters.terraformUnlockStateLockID, '') }}:
    - task: Bash@3
      displayName: Terraform unlocking locked state
      env:
        GOOGLE_PROJECT: ${{ parameters.googleCloudProject }}
        GOOGLE_REGION: ${{ parameters.googleCloudRegion }}
        GOOGLE_CREDENTIALS: $(downloadGCPKeyFile.secureFilePath)
        GOOGLE_IMPERSONATE_SERVICE_ACCOUNT: ${{ parameters.googleImpersonateServiceAccount }}
      inputs:
        targetType: 'inline'
        workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
        script: |
          set -eu  # fail on error
          trap "echo Error on line $LINENO" ERR

          echo "Unlocking locked state file with ID: ${{ parameters.terraformUnlockStateLockID }}"
          terraform ${{ parameters.terraformCLIGlobalOptionsForInit }} force-unlock -force ${{ parameters.terraformUnlockStateLockID }}
          echo "Finished unlocking locked state file"

  # Publish lock artifact for init stage (publishArtifact=true)
  - ${{ if eq(parameters.publishArtifact, true) }}:
    - task: PublishPipelineArtifact@1
      displayName: "Publish terraform lock file"
      inputs:
        targetPath: $(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}/.terraform.lock.hcl
        artifact: tflock