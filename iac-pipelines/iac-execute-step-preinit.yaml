parameters:
  # Common
  - name: environment
  - name: taskcondition
    default: succeeded()

  # GCP Auth
  - name: googleCloudProject
  - name: googleCloudRegion
  - name: googleImpersonateServiceAccount

  # ADO Auth
  # - name: azureDevOpsOrgServiceURL

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
  - name: terraformLogLevel
    default: 'INFO'

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForPlan
  - name: terraformCLIOptionsForPlan

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

  # Not exposed options
  - name: terraformDestroyPlan
    type: boolean
    default: false
  - name: tfvarsToUseFromPipelineVariables
    type: object
    default: {}

### Getting the tfvars and providers
steps:

  # Using fast-links.sh
  - task: Bash@3
    displayName: Getting the tfvars and providers
    name: fast-links
    condition: ${{ parameters.taskcondition }}
    env:
      GOOGLE_PROJECT: ${{ parameters.googleCloudProject }}
      GOOGLE_REGION: ${{ parameters.googleCloudRegion }}
      GOOGLE_CREDENTIALS: $(downloadGCPKeyFile.secureFilePath)
      GOOGLE_IMPERSONATE_SERVICE_ACCOUNT: ${{ parameters.googleImpersonateServiceAccount }}
      ARM_USE_OIDC: true
      ARM_OIDC_TOKEN: $(ARM_OIDC_TOKEN)
    inputs:
      targetType: 'inline'
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      script: |
        echo 'Test'