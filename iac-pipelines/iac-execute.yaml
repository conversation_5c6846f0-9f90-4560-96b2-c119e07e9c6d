parameters:
  # Common
  - name: action
  - name: environment
  - name: appCode
  - name: timeoutInMinutes
  - name: fabric
    default: false

  # GIT Prepare
  - name: tfModulesAZDOProject
    default: OTPHU-COE-TEMPLATESPEC

  # GCP Auth
  - name: googleCloudProject
    default: ''
  - name: googleCloudRegion
    default: ''
  - name: googleCloudKeyFile
    default: ''

  # ADO Auth
  # - name: azureDevOpsOrgServiceURL
  #   default: 'https://dev.azure.com/ADOS-OTPHU-01'

  # Azure Auth
  - name: armServiceConnection
    default: ''
  - name: subscriptionID
    default: ''

  # GCP Backend
  - name: googleBucketName
    default: ''
  - name: googleImpersonateServiceAccount
    default: ''

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
    default: ''
  - name: terraformVersion
    default: '1.9.8'
  - name: terraformExtraNoProxy
    default: ''
  - name: terraformRCFileForNetworkMirror
    default: '$(Pipeline.Workspace)/pipelines_repo/.config/.terraformrc'
  - name: terraformUnlockStateLockID
    default: ' '
  - name: terraformLogLevel
    default: 'ERROR'
  - name: terraformStateFilePathLegacy
    type: boolean
    default: false

  # Terraform CLI options
  # Init
  - name: terraformCLIGlobalOptionsForInit
    default: ''
  - name: terraformCLIOptionsForInit
    default: ''
  # Plan
  - name: terraformCLIGlobalOptionsForPlan
    default: ''
  - name: terraformCLIOptionsForPlan
    default: ''
  # Apply
  - name: terraformCLIGlobalOptionsForApply
    default: ''
  - name: terraformCLIOptionsForApply
    default: ''

  # Checkov parameters
  - name: skipCheckovScan
  - name: checkovExtraParams
    default: ''
  - name: terraformOutputVariablesToPipelineVariables
    type: object
    default: {}

  # Config parameters
  - name: config_directory
    default: ''
  - name: config_directory_dev
    default: ''
  - name: config_directory_prod
    default: ''
  - name: config_directory_override
    default: ''
  - name: config_repo
    default: ''
  - name: enableConfigStep
    type: boolean
    default: false

extends:
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/pipeline-generic.yaml
  template: iac-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    agentPoolNamePostfix: "AksPool-centralagent-Deploy"
    environment: ${{ split(parameters.environment, '-')[0] }}
    appCode: ${{ parameters.appcode }}
    pipelinetemplateRepoReference: self
    sonarQubeScanIsNotApplicableForThisApplication: true

###############################################
#### Initialization Stage - Terraform Plan ####
###############################################
    initializationStageJobTimeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    ### init -1 - keyvault
    # keyVaultServiceConnectionName:
    # KeyVaultName:
    # keyVaultCommaSeparatedSecretNames:
    # keyVaultRunAsPreJob:

    ### init 0
    # stepsPreInitializationStage:

    ### init 1
    stepsInitialization:
      #### Terraform init steps
      ##### Fabric: false
      - ${{ if ne(parameters.fabric, true) }}:
        - template: iac-execute-step-init.yaml
          parameters:
            # Config parameters - only for non-fabric
            ${{ if parameters.enableConfigStep }}:
              # Pass all config directory parameters for per-source handling
              config_directory: ${{ parameters.config_directory }}
              config_directory_dev: ${{ parameters.config_directory_dev }}
              config_directory_prod: ${{ parameters.config_directory_prod }}
              config_directory_override: ${{ parameters.config_directory_override }}
              config_repo: ${{ parameters.config_repo }}
              runConfigStep: true
            ${{ else }}:
              runConfigStep: false
            # isInitStage defaults to true

            # GIT Prepare
            tfModulesAZDOProject: ${{ parameters.tfModulesAZDOProject }}
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
            # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}
            armServiceConnection: ${{ parameters.armServiceConnection }}
            subscriptionID: ${{ parameters.subscriptionID }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            ${{ if parameters.terraformStateFilePathLegacy }}:
              terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
            ${{ else }}:
              terraformStateFilePath: ${{ format('{0}/{1}', variables['Build.Repository.Name'], replace(parameters.terraformProjectLocation, 'terraform/', '')) }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

            # Not exposed options
            publishArtifact: true

      ##### Fabric: true
      - ${{ if eq(parameters.fabric, true) }}:
        - template: iac-execute-step-init.yaml
          parameters:
            # No config parameters for fabric - runConfigStep and isInitStage default to false and true respectively
            isInitStage: true

            # GIT Prepare
            tfModulesAZDOProject: gcp-lz
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
            # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}
            armServiceConnection: ${{ parameters.armServiceConnection }}
            subscriptionID: ${{ parameters.subscriptionID }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            terraformStateFilePath: /

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

            # Not exposed options
            publishArtifact: true

      #### Terraform plan steps
      - template: iac-execute-step-plan.yaml
        parameters:
          # Common
          environment: ${{ parameters.environment }}

          # Auth
          googleCloudProject: ${{ parameters.googleCloudProject }}
          googleCloudRegion: ${{ parameters.googleCloudRegion }}
          googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
          # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformGlobalTFVars: ${{ parameters.terraformGlobalTFVars }}
          terraformLogLevel: ${{ parameters.terraformLogLevel }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
          terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}

          # Not exposed options
          ${{ if eq(parameters.action, 'destroy') }}:
            terraformDestroyPlan: true
          tfvarsToUseFromPipelineVariables: 
            - tfvarName: owner
              varName: $(Build.QueuedBy)
            - tfvarName: owner_dl
              varName: <EMAIL>

    ### init 2
    # stepsPostInitialization:

    ### init 3
    ## Checkov scan
    skipCheckovScan: ${{ parameters.skipCheckovScan }}

    ${{ if eq(parameters.checkovExtraParams, '') }}:
      checkovExtraParams: -d "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}" --var-file "$(Pipeline.Workspace)/config_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"
    ${{ else }}:
      checkovExtraParams: ${{ parameters.checkovExtraParams }}

    ### init 4
    # stepsPostInitializationStage:

    ### init 5
    # agentlessStepsPostInitializationStageJobTimeoutInMinutes:
    # agentlessStepsPostInitializationStageJobCondition:
    # agentlessStepsPostInitializationStage:


############################################
#### Deployment Stage - Terraform Apply ####
############################################
    deploymentStageJobTimeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    ## Run Condition
    ${{ if eq(parameters.action, 'plan') }}:
      deploymentStageCondition: false
    ${{ else }}:
      deploymentStageCondition: and(succeeded(), eq(dependencies.Initialization.outputs['Initialization.plan.terraform_changes_to_apply'], 'true'))

    ### deploy -1 - keyvault
    # keyVaultServiceConnectionName:
    # KeyVaultName:
    # keyVaultCommaSeparatedSecretNames:
    # keyVaultRunAsPreJob:

    ### deploy 0
    # stepsPreDeploymentStage:

    ### deploy 1
    stepsDeployment:
      ## Terraform init steps
      ##### Fabric: false
      - ${{ if ne(parameters.fabric, true) }}:
        - template: iac-execute-step-init.yaml
          parameters:
            # Config parameters - only for non-fabric
            ${{ if parameters.enableConfigStep }}:
              runConfigStep: true
            ${{ else }}:
              runConfigStep: false
            isInitStage: false

            # GIT Prepare
            tfModulesAZDOProject: ${{ parameters.tfModulesAZDOProject }}
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
            # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}
            armServiceConnection: ${{ parameters.armServiceConnection }}
            subscriptionID: ${{ parameters.subscriptionID }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            ${{ if parameters.terraformStateFilePathLegacy }}:
              terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
            ${{ else }}:
              terraformStateFilePath: ${{ format('{0}/{1}', variables['Build.Repository.Name'], replace(parameters.terraformProjectLocation, 'terraform/', '')) }}

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

      ##### Fabric: true
      - ${{ if eq(parameters.fabric, true) }}:
        - template: iac-execute-step-init.yaml
          parameters:
            # No config parameters for fabric - runConfigStep defaults to false
            isInitStage: false

            # GIT Prepare
            tfModulesAZDOProject: gcp-lz
            # Auth
            googleCloudProject: ${{ parameters.googleCloudProject }}
            googleCloudRegion: ${{ parameters.googleCloudRegion }}
            googleCloudKeyFile: ${{ parameters.googleCloudKeyFile }}
            googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
            # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}
            armServiceConnection: ${{ parameters.armServiceConnection }}
            subscriptionID: ${{ parameters.subscriptionID }}

            # GCP Backend
            googleBucketName: ${{ parameters.googleBucketName }}
            # terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
            terraformStateFilePath: /

            # Terraform configuration
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
            terraformVersion: ${{ parameters.terraformVersion }}
            terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
            terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
            ${{ if ne(parameters.terraformUnlockStateLockID, ' ') }}:
              terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

            # Terraform CLI options
            terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
            terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

      ## Terraform apply steps
      - template: iac-execute-step-apply.yaml
        parameters:
          # Auth
          googleCloudProject: ${{ parameters.googleCloudProject }}
          googleCloudRegion: ${{ parameters.googleCloudRegion }}
          googleImpersonateServiceAccount: ${{ parameters.googleImpersonateServiceAccount }}
          # azureDevOpsOrgServiceURL: ${{ parameters.azureDevOpsOrgServiceURL }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformLogLevel: ${{ parameters.terraformLogLevel }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
          terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}

      ## Post apply steps --> move to stepsPostDeployment?
      - ${{ each item in parameters.terraformOutputVariablesToPipelineVariables }}:
        - template: iac-common/step-tf-output-to-pipeline-var.yaml@pipelinetemplates
          parameters:
            terraformOutputVariableName: ${{ item.terraformVariableName }}
            pipelineVariableName: ${{ item.pipelineVariableName }}
            pipelineVariableIsSecret: ${{ item.pipelineVariableIsSecret }}
            terraformProjectLocation: ${{ parameters.terraformProjectLocation }}