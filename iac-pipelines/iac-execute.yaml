parameters:
  # Common
  - name: action
  - name: environment
  - name: appCode
  - name: timeoutInMinutes

  # GIT Prepare
  - name: tfModulesAZDOProject
    default: OTPHU-COE-TEMPLATESPEC

  # AWS Auth
  - name: awsServiceConnectionName
  - name: awsRegion

  # AWS Backend
  - name: awsS3BucketName
  - name: awsS3BucketRegion
  - name: awsDynamoDBTableName
    default: ''

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
    default: ''
  - name: terraformVersion
    default: '1.9.8'
  - name: terraformExtraNoProxy
    default: ''
  - name: terraformRCFileForNetworkMirror
    default: '$(Pipeline.Workspace)/pipelines_repo/.config/.terraformrc'
  - name: terraformUnlockStateLockID
    default: ' '
  - name: terraformLogLevel
    default: 'ERROR'
  - name: terraformStateFilePathLegacy
    type: boolean
    default: false

  # Terraform CLI options
  # Init
  - name: terraformCLIGlobalOptionsForInit
    default: ''
  - name: terraformCLIOptionsForInit
    default: ''
  # Plan
  - name: terraformCLIGlobalOptionsForPlan
    default: ''
  - name: terraformCLIOptionsForPlan
    default: ''
  # Apply
  - name: terraformCLIGlobalOptionsForApply
    default: ''
  - name: terraformCLIOptionsForApply
    default: ''

  # Checkov parameters
  - name: skipCheckovScan
  - name: checkovExtraParams
    default: ''
  - name: terraformOutputVariablesToPipelineVariables
    type: object
    default: {}

  # Config parameters
  - name: config_directory
    default: ''
  - name: config_directory_dev
    default: ''
  - name: config_directory_prod
    default: ''
  - name: config_directory_override
    default: ''
  - name: config_repo
    default: ''
  - name: enableConfigStep
    type: boolean
    default: false

extends:
  # https://dev.azure.com/ADOS-OTPHU-01/OTPHU-CDO-ADOS-TOOLS/_git/pipelinetemplates?path=/iac-common/pipeline-generic.yaml
  template: iac-common/pipeline-generic.yaml@pipelinetemplates
  parameters:
    agentPoolNamePostfix: "AksPool-centralagent-Deploy"
    environment: ${{ split(parameters.environment, '-')[0] }}
    appCode: ${{ parameters.appcode }}
    pipelinetemplateRepoReference: self
    sonarQubeScanIsNotApplicableForThisApplication: true
    timeoutInMinutes: ${{ parameters.timeoutInMinutes }}

    ### init 1
    stepsInitialization:
      #### Terraform init steps for AWS
      - template: iac-execute-step-init-aws.yaml
        parameters:
          # Config parameters
          ${{ if parameters.enableConfigStep }}:
            runConfigStep: true
          ${{ else }}:
            runConfigStep: false
          # isInitStage defaults to true

          # GIT Prepare
          tfModulesAZDOProject: ${{ parameters.tfModulesAZDOProject }}
          # AWS Auth
          awsServiceConnectionName: ${{ parameters.awsServiceConnectionName }}
          awsRegion: ${{ parameters.awsRegion }}

          # AWS Backend
          awsS3BucketName: ${{ parameters.awsS3BucketName }}
          awsS3BucketRegion: ${{ parameters.awsS3BucketRegion }}
          awsDynamoDBTableName: ${{ parameters.awsDynamoDBTableName }}
          ${{ if parameters.terraformStateFilePathLegacy }}:
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
          ${{ else }}:
            terraformStateFilePath: ${{ format('{0}/{1}', variables['Build.Repository.Name'], replace(parameters.terraformProjectLocation, 'terraform/', '')) }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformVersion: ${{ parameters.terraformVersion }}
          terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
          terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
          terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
          terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

          # Not exposed options
          publishArtifact: true

      ## Terraform plan steps
      - template: iac-execute-step-plan-aws.yaml
        parameters:
          # Common
          environment: ${{ parameters.environment }}
          terraformGlobalTFVars: ${{ parameters.terraformGlobalTFVars }}

          # AWS Auth
          awsServiceConnectionName: ${{ parameters.awsServiceConnectionName }}
          awsRegion: ${{ parameters.awsRegion }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformLogLevel: ${{ parameters.terraformLogLevel }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForPlan: ${{ parameters.terraformCLIGlobalOptionsForPlan }}
          terraformCLIOptionsForPlan: ${{ parameters.terraformCLIOptionsForPlan }}

          # Not exposed options
          ${{ if eq(parameters.action, 'destroy') }}:
            terraformDestroyPlan: true
          tfvarsToUseFromPipelineVariables: 
            - tfvarName: owner
              varName: $(Build.QueuedBy)
            - tfvarName: owner_dl
              varName: <EMAIL>

    ### init 2
    # stepsPostInitialization:

    ### init 3
    ## Checkov scan
    skipCheckovScan: ${{ parameters.skipCheckovScan }}

    ${{ if eq(parameters.checkovExtraParams, '') }}:
      checkovExtraParams: -d "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}" --var-file "$(Pipeline.Workspace)/config_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"
    ${{ else }}:
      checkovExtraParams: ${{ parameters.checkovExtraParams }}

    ### deploy 1
    stepsDeployment:
      ## Terraform init steps for AWS
      - template: iac-execute-step-init-aws.yaml
        parameters:
          # Config parameters
          ${{ if parameters.enableConfigStep }}:
            runConfigStep: true
          ${{ else }}:
            runConfigStep: false
          isInitStage: false

          # GIT Prepare
          tfModulesAZDOProject: ${{ parameters.tfModulesAZDOProject }}
          # AWS Auth
          awsServiceConnectionName: ${{ parameters.awsServiceConnectionName }}
          awsRegion: ${{ parameters.awsRegion }}

          # AWS Backend
          awsS3BucketName: ${{ parameters.awsS3BucketName }}
          awsS3BucketRegion: ${{ parameters.awsS3BucketRegion }}
          awsDynamoDBTableName: ${{ parameters.awsDynamoDBTableName }}
          ${{ if parameters.terraformStateFilePathLegacy }}:
            terraformStateFilePath: ${{ format('{0}.{1}.tfstate', variables['Build.Repository.Name'], coalesce(split(parameters.terraformProjectLocation, '/')[2], split(parameters.terraformProjectLocation, '/')[1])) }}
          ${{ else }}:
            terraformStateFilePath: ${{ format('{0}/{1}', variables['Build.Repository.Name'], replace(parameters.terraformProjectLocation, 'terraform/', '')) }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformVersion: ${{ parameters.terraformVersion }}
          terraformExtraNoProxy: ${{ parameters.terraformExtraNoProxy }}
          terraformRCFileForNetworkMirror: ${{ parameters.terraformRCFileForNetworkMirror }}
          terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForInit: ${{ parameters.terraformCLIGlobalOptionsForInit }}
          terraformCLIOptionsForInit: ${{ parameters.terraformCLIOptionsForInit }}

          # Not exposed options
          publishArtifact: false

      ## Terraform apply steps
      - template: iac-execute-step-apply-aws.yaml
        parameters:
          # AWS Auth
          awsServiceConnectionName: ${{ parameters.awsServiceConnectionName }}
          awsRegion: ${{ parameters.awsRegion }}

          # Terraform configuration
          terraformProjectLocation: ${{ parameters.terraformProjectLocation }}
          terraformLogLevel: ${{ parameters.terraformLogLevel }}

          # Terraform CLI options
          terraformCLIGlobalOptionsForApply: ${{ parameters.terraformCLIGlobalOptionsForApply }}
          terraformCLIOptionsForApply: ${{ parameters.terraformCLIOptionsForApply }}

      ## Post apply steps --> move to stepsPostDeployment?
      # Output variables to pipeline variables
      - ${{ each var in parameters.terraformOutputVariablesToPipelineVariables }}:
        - task: Bash@3
          displayName: "Set pipeline variable ${{ var.pipelineVariableName }} from terraform output ${{ var.terraformOutputName }}"
          inputs:
            targetType: 'inline'
            workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
            script: |
              set -eu  # fail on error
              trap "echo Error on line $LINENO" ERR
              
              terraform_output_value=$(terraform output -raw ${{ var.terraformOutputName }})
              echo "##vso[task.setvariable variable=${{ var.pipelineVariableName }};isOutput=true]$terraform_output_value"
