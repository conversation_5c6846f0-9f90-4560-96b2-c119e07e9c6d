# GCP-LZ Pipelines Repo

## v0.9.0 - Jun 16, 2025 [current]
FEATURES:
- Added iac-execute-step-config.yaml pipeline template which enabling terraform configuration loads with json, tfvars and yaml files

ENHANCEMENTS:
- Added the googleEnvironment variable to LZ env files

BUG FIXES:
- Fixed fabric env files appCode variable

## v0.8.0 - May 26, 2025
ENHANCEMENTS:
- Added Cloud Fundation Fabric support with the new `fabric` (bool) input parameter
- Added PRD-gcp-security-1-vpcsc.yaml to environments

## v0.7.0 - Mar 10, 2025
ENHANCEMENTS:
- Added bucket and impersonate_service_account variables to environment tfvars files

## v0.6.0 - Feb 28, 2025
FEATURES:
- Added Landing Zones Prod environment files
- Updated Landing Zones Test/Prod key file path to the corresponding delegator service account

## v0.5.0 - Feb 11, 2025
FEATURES:
- Added AzureRM OIDC login support
- Updated lztest env yamls with Azure service connection details

BUG FIXES:
- Fixed terraformUnlockStateLockID for GCP buckets

## v0.4.2 - Feb 5, 2025
FEATURES:
- Added lztest-rpr-projects and lztest-rpd-projectsenvironment files

## v0.4.1 - Feb 5, 2025
FEATURES:
- Added lztest-stt-projects environment files

## v0.4.0 - Jan 29, 2025
FEATURES:
- Added the terraformGlobalTFVars input parameter, to handle context test subsidiary environment types

## v0.3.1 - Dec 15, 2024
BUG FIXES:
- Minor documentation fixes

## v0.3.0 - Dec 15, 2024
ENHANCEMENTS:
- Added PR template

BUG FIXES:
- Replaced Default Application Credentials authentication in favor or GOOGLE_CREDENTIALS and GOOGLE_IMPERSONATE_SERVICE_ACCOUNT env vars, as impersonation was broken with DAC.

## v0.2.0 - Dec 4, 2024
ENHANCEMENTS:
- Cleaned up yaml structure

## v0.1.0 - Dec 4, 2024
FEATURES:
- Initial version