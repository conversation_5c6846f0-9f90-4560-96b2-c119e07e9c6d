# AWS Pipeline Infrastructure

This repository contains AWS-specific pipeline infrastructure for Terraform deployments, adapted from the GCP pipeline structure in the tooling repository.

## Overview

The AWS pipeline infrastructure provides:

- **AWS-native authentication** using service connections
- **S3 backend** for Terraform state storage with optional DynamoDB locking
- **Complete Terraform lifecycle** support (init, plan, apply, destroy, test)
- **Security scanning** integration with Checkov
- **Artifact management** for plans and state files
- **Environment-specific** configuration management

## Architecture

### Core Components

- `iac-pipelines/iac-execute.yaml` - Main AWS pipeline template
- `iac-pipelines/iac-execute-step-init-aws.yaml` - AWS initialization steps
- `iac-pipelines/iac-execute-step-plan-aws.yaml` - AWS planning steps
- `iac-pipelines/iac-execute-step-apply-aws.yaml` - AWS apply/destroy steps
- `iac-pipelines/iac-generic-step-terraformrc.yaml` - Terraform configuration
- `env/` - Environment-specific variable files
- `examples/` - Usage examples

### Key Features

✅ **AWS Service Connection Authentication**
- Uses `AWSShellScript@1` tasks for secure AWS operations
- Automatic credential management through Azure DevOps service connections

✅ **S3 Backend with DynamoDB Locking**
- Configurable S3 bucket for state storage
- Optional DynamoDB table for state locking
- Region-specific backend configuration

✅ **Terraform Operations**
- Initialize with backend configuration
- Plan with detailed exit codes and artifact publishing
- Apply with plan validation and execution
- Destroy with safety warnings
- Test mode for validation workflows

✅ **Security & Compliance**
- Integrated Checkov security scanning
- Configurable scan parameters
- Security warnings for destructive operations

## Usage

### Quick Start

1. **Configure AWS Service Connection** in Azure DevOps
2. **Set up S3 bucket** for Terraform state storage
3. **Create DynamoDB table** for state locking (optional)
4. **Configure pipeline variables** (see Variables section)
5. **Use the pipeline template** in your repository

### Example Pipeline

```yaml
extends:
  template: iac-pipelines/iac-execute.yaml@pipelines
  parameters:
    action: apply
    environment: EDV-aws-iactest
    terraformProjectLocation: terraform/aws/vpc
    awsServiceConnectionName: $(AWS_SERVICE_CONNECTION)
    awsRegion: us-east-1
    awsS3BucketName: $(AWS_TERRAFORM_STATE_BUCKET)
    awsS3BucketRegion: us-east-1
    awsDynamoDBTableName: $(AWS_TERRAFORM_LOCK_TABLE)
```

See `examples/aws-pipeline-example.yaml` for a complete example.

## Required Variables

Configure these variables in Azure DevOps:

### AWS Authentication
- `AWS_SERVICE_CONNECTION` - Name of AWS service connection
- `awsRegion` - AWS region for operations

### AWS Backend
- `AWS_TERRAFORM_STATE_BUCKET` - S3 bucket for state storage
- `awsS3BucketRegion` - S3 bucket region
- `AWS_TERRAFORM_LOCK_TABLE` - DynamoDB table for locking (optional)

### Application
- `appCode` - Application identifier

## Supported Actions

- **`plan`** - Generate and review Terraform plan
- **`apply`** - Apply Terraform changes
- **`destroy`** - Destroy infrastructure (with warnings)
- **`test`** - Apply, validate, then destroy

## Prerequisites

1. **AWS Service Connection** configured in Azure DevOps with appropriate permissions
2. **S3 Bucket** created for Terraform state storage
3. **DynamoDB Table** created for state locking (optional but recommended)
4. **IAM Permissions** for the service connection to manage target resources

## Environment Files

Environment-specific configurations are stored in `env/` directory:
- `env/EDV-aws-iactest.yaml` - Development environment
- `env/TST-iac.yaml` - Test environment
- `env/PRD-iac.yaml` - Production environment

## Migration from GCP

This AWS pipeline infrastructure was created by adapting the GCP pipeline structure from the tooling repository. Key differences:

- **Authentication**: Uses AWS service connections instead of GCP key files
- **Backend**: Uses S3 + DynamoDB instead of GCS
- **Tasks**: Uses `AWSShellScript@1` instead of `Bash@3` with GCP environment variables
- **Configuration**: AWS-specific parameters and backend configuration

## Next Steps

1. **Test the pipeline** with a simple Terraform configuration
2. **Configure monitoring** and alerting for pipeline failures
3. **Set up branch policies** for production deployments
4. **Create additional environment** configurations as needed

---

**Status**: ✅ Functional AWS pipeline infrastructure ready for use