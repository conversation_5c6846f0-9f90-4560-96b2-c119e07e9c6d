# Introduction

Dedicated Pipelines repository for gcp-lz Azure DevOps Project

## IaC Execute

Example configuration

```yaml
trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.state, '/')[2], split(parameters.state, '/')[1]) }} • ${{ parameters.action }}
OR
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ replace(parameters.state,'/','／') }} • ${{ parameters.action }}
${{ replace(parameters.state,'/','／') }}

parameters:
- name: environment
  type: string
  default: PRD-gcp-automation
  values:
  - PRD-gcp-automation

- name: state
  type: string
  default: main
  values:
  - main

- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: ' '

- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@pipelines

resources:
  repositories:
    - repository: pipelines
      type: git
      name: pipelines
      ref: refs/tags/v0.5.0
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@pipelines
  parameters:
    cloud: gcp
    action: ${{ parameters.action }}
    environment: ${{ parameters.environment }}
    terraformProjectLocation: terraform/${{ parameters.state }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    # terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
    skipCheckovScan: ${{ parameters.skip_checkov }}
    appCode: ${{ variables.appCode }}
    googleCloudProject: ${{ variables.googleCloudProject }}
    googleCloudRegion: ${{ variables.googleCloudRegion }}
    googleCloudKeyFile: ${{ variables.googleCloudKeyFile }}
    googleBucketName: ${{ variables.googleBucketName }}
    googleImpersonateServiceAccount: ${{ variables.googleImpersonateServiceAccount }}
    armServiceConnection: ${{ variables.armServiceConnection }}
    subscriptionID: ${{ variables.subscriptionID }}
    terraformVersion: '1.9.8'
```

## GCP LZ SemVer

Provides quick versioning via repository webhook actions

Webhook URL: https://dev.azure.com/ADOS-OTPHU-01/_apis/public/distributedtask/webhooks/gcp_lz_semver?api-version=6.0-preview
